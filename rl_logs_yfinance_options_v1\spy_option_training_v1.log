2025-09-10 00:54:36,094 - INFO - SPY.py:2359 - Logging configured for Option Trader v1.
2025-09-10 00:54:36,094 - INFO - SPY.py:2360 - Log file: c:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-10 00:54:36,095 - INFO - SPY.py:2361 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-10 00:54:36,095 - INFO - SPY.py:2369 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-10 00:54:36,097 - INFO - SPY.py:5948 - --- Option Trader v3.1 ---
2025-09-10 00:54:36,097 - INFO - SPY.py:6050 - [INFO] Main: Preparing Data for Training
2025-09-10 00:54:36,097 - INFO - SPY.py:6063 - Fetching train data for SPY (elapsed: 0.0s)
2025-09-10 00:54:37,786 - INFO - SPY.py:2651 - SPY expanded feature set: Keeping OHLC columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-10 00:54:37,786 - INFO - SPY.py:2657 - Using expanded OHLC feature set for SPY
2025-09-10 00:54:37,786 - INFO - SPY.py:2660 - SPY: Keeping OHLC columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-10 00:54:37,788 - INFO - SPY.py:2688 - Processed SPY data shape: (501, 4). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-10 00:54:37,788 - INFO - SPY.py:2689 - Data quality check: 0/2004 (0.0%) zero values
2025-09-10 00:54:37,788 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1757436876 SUCCESS in 1.69s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-10 00:54:37,789 - INFO - SPY.py:6100 - SUCCESS: Validated authentic training data for SPY: 501 rows
2025-09-10 00:54:39,790 - INFO - SPY.py:6063 - Fetching train data for ^VIX (elapsed: 3.7s)
2025-09-10 00:54:40,593 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-10 00:54:41,579 - INFO - SPY.py:2532 - Processing ^VIX data with shape (501, 7)
2025-09-10 00:54:41,579 - INFO - SPY.py:2533 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-10 00:54:41,580 - INFO - SPY.py:2535 - Raw ^VIX Close values - first: 32.95000076293945, last: 11.9399995803833
2025-09-10 00:54:41,582 - INFO - SPY.py:2455 - Applying VIX validation for VIX
2025-09-10 00:54:41,583 - INFO - SPY.py:2614 - Processed VIX close values - first: 32.95000076293945, last: 11.9399995803833
2025-09-10 00:54:41,583 - INFO - SPY.py:2647 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-10 00:54:41,584 - INFO - SPY.py:2688 - Processed ^VIX data shape: (501, 1). Columns: ['close_VIX']
2025-09-10 00:54:41,585 - INFO - SPY.py:2689 - Data quality check: 0/501 (0.0%) zero values
2025-09-10 00:54:41,585 - INFO - SPY.py:1161 - VALIDATION: ^VIX last value: 11.9399995803833
2025-09-10 00:54:41,585 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1757436879 SUCCESS in 1.79s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-10 00:54:41,585 - INFO - SPY.py:6100 - SUCCESS: Validated authentic training data for ^VIX: 501 rows
2025-09-10 00:54:43,586 - INFO - SPY.py:6063 - Fetching train data for ^VIX3M (elapsed: 7.5s)
2025-09-10 00:54:44,389 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 25s
2025-09-10 00:54:45,763 - INFO - SPY.py:2647 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-10 00:54:45,764 - INFO - SPY.py:2688 - Processed ^VIX3M data shape: (501, 1). Columns: ['close_VIX3M']
2025-09-10 00:54:45,764 - INFO - SPY.py:2689 - Data quality check: 0/501 (0.0%) zero values
2025-09-10 00:54:45,764 - INFO - SPY.py:1161 - VALIDATION: ^VIX3M last value: 14.170000076293945
2025-09-10 00:54:45,764 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1757436883 SUCCESS in 2.18s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-10 00:54:45,764 - INFO - SPY.py:6100 - SUCCESS: Validated authentic training data for ^VIX3M: 501 rows
2025-09-10 00:54:47,766 - INFO - SPY.py:6063 - Fetching train data for ^IRX (elapsed: 11.7s)
2025-09-10 00:54:48,568 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-10 00:54:49,364 - INFO - SPY.py:2532 - Processing ^IRX data with shape (501, 7)
2025-09-10 00:54:49,364 - INFO - SPY.py:2533 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-10 00:54:49,364 - INFO - SPY.py:2535 - Raw ^IRX Close values - first: 1.4830000400543213, last: 5.239999771118164
2025-09-10 00:54:49,366 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-09-10 00:54:49,367 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-10 00:54:49,367 - INFO - SPY.py:2482 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-09-10 00:54:49,368 - INFO - SPY.py:2485 - IRX (3-month Treasury yield) after conversion to decimal: min=0.014830, max=0.053480, median=0.051530
2025-09-10 00:54:49,369 - INFO - SPY.py:2614 - Processed IRX (3-month Treasury yield) close values - first: 0.014830000400543213, last: 0.05239999771118164
2025-09-10 00:54:49,369 - INFO - SPY.py:2621 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.052400 (5.2400%)
2025-09-10 00:54:49,370 - INFO - SPY.py:2647 - Market index ^IRX: Keeping only ['close_IRX']
2025-09-10 00:54:49,371 - INFO - SPY.py:2688 - Processed ^IRX data shape: (501, 1). Columns: ['close_IRX']
2025-09-10 00:54:49,372 - INFO - SPY.py:2689 - Data quality check: 0/501 (0.0%) zero values
2025-09-10 00:54:49,372 - INFO - SPY.py:1161 - VALIDATION: ^IRX last value: 0.05239999771118164
2025-09-10 00:54:49,372 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1757436887 SUCCESS in 1.61s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-09-10 00:54:49,372 - INFO - SPY.py:6100 - SUCCESS: Validated authentic training data for ^IRX: 501 rows
2025-09-10 00:54:51,374 - INFO - SPY.py:6063 - Fetching train data for ^TNX (elapsed: 15.3s)
2025-09-10 00:54:52,177 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-10 00:54:52,781 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-10 00:54:52,782 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-10 00:54:52,782 - INFO - SPY.py:2482 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-10 00:54:52,782 - INFO - SPY.py:2485 - TNX (10-year Treasury yield) after conversion to decimal: min=0.026060, max=0.049880, median=0.039220
2025-09-10 00:54:52,783 - INFO - SPY.py:2614 - Processed TNX (10-year Treasury yield) close values - first: 0.03306999921798706, last: 0.0423799991607666
2025-09-10 00:54:52,783 - INFO - SPY.py:2621 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.042380 (4.2380%)
2025-09-10 00:54:52,784 - INFO - SPY.py:2647 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-10 00:54:52,785 - INFO - SPY.py:2688 - Processed ^TNX data shape: (501, 1). Columns: ['close_TNX']
2025-09-10 00:54:52,785 - INFO - SPY.py:2689 - Data quality check: 0/501 (0.0%) zero values
2025-09-10 00:54:52,785 - INFO - SPY.py:1161 - VALIDATION: ^TNX last value: 0.0423799991607666
2025-09-10 00:54:52,785 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1757436891 SUCCESS in 1.41s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-10 00:54:52,786 - INFO - SPY.py:6100 - SUCCESS: Validated authentic training data for ^TNX: 501 rows
2025-09-10 00:54:52,786 - INFO - SPY.py:6114 - SUCCESS: All training data validated as authentic and complete
2025-09-10 00:54:52,786 - INFO - SPY.py:6117 - Creating combined train features...
2025-09-10 00:54:52,793 - INFO - SPY.py:2881 - Created master index with 501 unique dates from 2022-06-16 to 2024-06-13
2025-09-10 00:54:52,793 - INFO - SPY.py:2886 - Processing ticker SPY for reindexing: shape=(501, 4), columns=['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-10 00:54:52,793 - INFO - SPY.py:2892 - About to reindex SPY with master_index length 501
2025-09-10 00:54:52,794 - INFO - SPY.py:2749 - Index overlap for SPY: 100.0% (501/501 dates)
2025-09-10 00:54:52,796 - INFO - SPY.py:2812 - Successfully reindexed SPY with validation passed
2025-09-10 00:54:52,797 - INFO - SPY.py:2896 - Successfully reindexed SPY to master index. New shape: (501, 4)
2025-09-10 00:54:52,797 - INFO - SPY.py:2886 - Processing ticker ^VIX for reindexing: shape=(501, 1), columns=['close_VIX']
2025-09-10 00:54:52,797 - INFO - SPY.py:2892 - About to reindex ^VIX with master_index length 501
2025-09-10 00:54:52,798 - INFO - SPY.py:2749 - Index overlap for ^VIX: 100.0% (501/501 dates)
2025-09-10 00:54:52,799 - INFO - SPY.py:2812 - Successfully reindexed ^VIX with validation passed
2025-09-10 00:54:52,799 - INFO - SPY.py:2896 - Successfully reindexed ^VIX to master index. New shape: (501, 1)
2025-09-10 00:54:52,799 - INFO - SPY.py:2886 - Processing ticker ^VIX3M for reindexing: shape=(501, 1), columns=['close_VIX3M']
2025-09-10 00:54:52,799 - INFO - SPY.py:2892 - About to reindex ^VIX3M with master_index length 501
2025-09-10 00:54:52,800 - INFO - SPY.py:2749 - Index overlap for ^VIX3M: 100.0% (501/501 dates)
2025-09-10 00:54:52,801 - INFO - SPY.py:2812 - Successfully reindexed ^VIX3M with validation passed
2025-09-10 00:54:52,801 - INFO - SPY.py:2896 - Successfully reindexed ^VIX3M to master index. New shape: (501, 1)
2025-09-10 00:54:52,801 - INFO - SPY.py:2886 - Processing ticker ^IRX for reindexing: shape=(501, 1), columns=['close_IRX']
2025-09-10 00:54:52,801 - INFO - SPY.py:2892 - About to reindex ^IRX with master_index length 501
2025-09-10 00:54:52,802 - INFO - SPY.py:2749 - Index overlap for ^IRX: 100.0% (501/501 dates)
2025-09-10 00:54:52,802 - INFO - SPY.py:2812 - Successfully reindexed ^IRX with validation passed
2025-09-10 00:54:52,803 - INFO - SPY.py:2896 - Successfully reindexed ^IRX to master index. New shape: (501, 1)
2025-09-10 00:54:52,803 - INFO - SPY.py:2886 - Processing ticker ^TNX for reindexing: shape=(501, 1), columns=['close_TNX']
2025-09-10 00:54:52,803 - INFO - SPY.py:2892 - About to reindex ^TNX with master_index length 501
2025-09-10 00:54:52,803 - INFO - SPY.py:2749 - Index overlap for ^TNX: 100.0% (501/501 dates)
2025-09-10 00:54:52,804 - INFO - SPY.py:2812 - Successfully reindexed ^TNX with validation passed
2025-09-10 00:54:52,805 - INFO - SPY.py:2896 - Successfully reindexed ^TNX to master index. New shape: (501, 1)
2025-09-10 00:54:52,805 - INFO - SPY.py:2914 - Starting combine features with SPY shape: (501, 4)
2025-09-10 00:54:52,805 - INFO - SPY.py:2915 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-10 00:54:52,805 - INFO - SPY.py:2931 - Merging ^VIX (Shape: (501, 1), Columns: ['close_VIX'])
2025-09-10 00:54:52,805 - INFO - SPY.py:2941 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-09-10 00:54:52,806 - INFO - SPY.py:2977 - SPY index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,806 - INFO - SPY.py:2978 - ^VIX index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,807 - INFO - SPY.py:2995 - ^VIX close_VIX sample after join (first 3): [32.95000076293945, 31.1299991607666, 30.190000534057617], last: 11.9399995803833
2025-09-10 00:54:52,808 - INFO - SPY.py:3013 - After joining ^VIX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX']
2025-09-10 00:54:52,808 - INFO - SPY.py:2931 - Merging ^VIX3M (Shape: (501, 1), Columns: ['close_VIX3M'])
2025-09-10 00:54:52,808 - INFO - SPY.py:2941 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-09-10 00:54:52,809 - INFO - SPY.py:2977 - SPY index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,809 - INFO - SPY.py:2978 - ^VIX3M index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,810 - INFO - SPY.py:2995 - ^VIX3M close_VIX3M sample after join (first 3): [33.4900016784668, 32.36000061035156, 31.25], last: 14.170000076293945
2025-09-10 00:54:52,810 - INFO - SPY.py:3013 - After joining ^VIX3M, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-10 00:54:52,810 - INFO - SPY.py:2931 - Merging ^IRX (Shape: (501, 1), Columns: ['close_IRX'])
2025-09-10 00:54:52,811 - INFO - SPY.py:2941 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-09-10 00:54:52,811 - INFO - SPY.py:2977 - SPY index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,811 - INFO - SPY.py:2978 - ^IRX index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,812 - INFO - SPY.py:2995 - ^IRX close_IRX sample after join (first 3): [0.014830000400543213, 0.015099999904632568, 0.014880000352859498], last: 0.05239999771118164
2025-09-10 00:54:52,812 - INFO - SPY.py:3013 - After joining ^IRX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-09-10 00:54:52,812 - INFO - SPY.py:2931 - Merging ^TNX (Shape: (501, 1), Columns: ['close_TNX'])
2025-09-10 00:54:52,813 - INFO - SPY.py:2941 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-09-10 00:54:52,813 - INFO - SPY.py:2977 - SPY index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,813 - INFO - SPY.py:2978 - ^TNX index range: 2022-06-16 to 2024-06-13, count: 501
2025-09-10 00:54:52,814 - INFO - SPY.py:2995 - ^TNX close_TNX sample after join (first 3): [0.03306999921798706, 0.032390000820159914, 0.03306999921798706], last: 0.0423799991607666
2025-09-10 00:54:52,814 - INFO - SPY.py:3013 - After joining ^TNX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-10 00:54:52,814 - INFO - SPY.py:3024 - Shape after merging all tickers: (501, 8)
2025-09-10 00:54:52,815 - INFO - SPY.py:3287 - Skipping VIX derived feature calculations - using reduced feature set
2025-09-10 00:54:52,816 - INFO - SPY.py:3291 - Skipping Treasury derived feature calculations - using reduced feature set
2025-09-10 00:54:52,816 - INFO - SPY.py:3113 - Volume column volume_SPY not found, skipping volume normalization
2025-09-10 00:54:52,816 - INFO - SPY.py:3116 - Performing final validation and cleanup...
2025-09-10 00:54:52,816 - ERROR - SPY.py:3127 - AUTHENTIC DATA VALIDATION FAILED: Missing required columns for feature calculations: ['volume_SPY']
2025-09-10 00:54:52,817 - ERROR - SPY.py:6121 - Failed to create combined features from training data: AUTHENTIC DATA VALIDATION FAILED: Missing required columns for feature calculations: ['volume_SPY']
2025-09-10 00:54:52,817 - CRITICAL - SPY.py:6122 - STOPPING SCRIPT: Cannot create features from authentic training data. Script will not proceed.
2025-09-10 00:54:52,817 - INFO - SPY.py:9384 - [INFO] Main: Script execution completed
2025-09-10 00:54:52,817 - INFO - SPY.py:779 - [INFO] PerformanceMonitor: Performance Summary: 5/5 operations successful (100.0% success rate)
2025-09-10 00:54:52,817 - INFO - SPY.py:787 - [INFO] PerformanceMonitor: Average refactored_fetch time: 1.74s
